<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\HttpKernel\CacheClearer;

/**
 * CacheClearerInterface.
 *
 * <AUTHOR> <d<PERSON><PERSON><PERSON>@gmail.com>
 */
interface CacheClearerInterface
{
    /**
     * Clears any caches necessary.
     */
    public function clear(string $cacheDir): void;
}
