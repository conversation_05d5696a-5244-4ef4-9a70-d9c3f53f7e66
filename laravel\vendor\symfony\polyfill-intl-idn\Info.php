<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>> and <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Polyfill\Intl\Idn;

/**
 * @internal
 */
class Info
{
    public $bidiDomain = false;
    public $errors = 0;
    public $validBidiDomain = true;
    public $transitionalDifferent = false;
}
