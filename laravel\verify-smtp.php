<?php

// This script verifies SMTP credentials by attempting a direct connection
// Run with: php verify-smtp.php

// Function to test SMTP connection
function testSmtpConnection($host, $port, $username, $password, $encryption = null) {
    echo "Testing SMTP connection to $host:$port with username $username...\n";
    
    try {
        // Create a socket connection to the SMTP server
        $socket = fsockopen(
            ($encryption == 'ssl' ? 'ssl://' : '') . $host,
            $port,
            $errno,
            $errstr,
            30
        );
        
        if (!$socket) {
            echo "❌ Failed to connect: $errstr ($errno)\n";
            return false;
        }
        
        // Read the server greeting
        $response = fgets($socket, 515);
        echo "Server greeting: $response";
        
        // Send EHLO command
        fputs($socket, "EHLO " . gethostname() . "\r\n");
        $response = '';
        while ($line = fgets($socket, 515)) {
            $response .= $line;
            if (substr($line, 3, 1) == ' ') break;
        }
        echo "EHLO response: $response";
        
        // Check if authentication is supported
        if (strpos($response, 'AUTH') === false) {
            echo "❌ Server does not support authentication\n";
            fclose($socket);
            return false;
        }
        
        // Start TLS if needed and not already using SSL
        if ($encryption == 'tls' && strpos($response, 'STARTTLS') !== false) {
            fputs($socket, "STARTTLS\r\n");
            $response = fgets($socket, 515);
            echo "STARTTLS response: $response";
            
            if (stream_socket_enable_crypto($socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                // Re-send EHLO after TLS
                fputs($socket, "EHLO " . gethostname() . "\r\n");
                $response = '';
                while ($line = fgets($socket, 515)) {
                    $response .= $line;
                    if (substr($line, 3, 1) == ' ') break;
                }
                echo "EHLO (after TLS) response: $response";
            } else {
                echo "❌ Failed to enable TLS\n";
                fclose($socket);
                return false;
            }
        }
        
        // Try to authenticate
        fputs($socket, "AUTH LOGIN\r\n");
        $response = fgets($socket, 515);
        echo "AUTH LOGIN response: $response";
        
        fputs($socket, base64_encode($username) . "\r\n");
        $response = fgets($socket, 515);
        echo "Username response: $response";
        
        fputs($socket, base64_encode($password) . "\r\n");
        $response = fgets($socket, 515);
        echo "Password response: $response";
        
        // Check if authentication was successful
        if (substr($response, 0, 3) == '235') {
            echo "✅ Authentication successful!\n";
            
            // Send QUIT command
            fputs($socket, "QUIT\r\n");
            fclose($socket);
            return true;
        } else {
            echo "❌ Authentication failed: $response\n";
            
            // Send QUIT command
            fputs($socket, "QUIT\r\n");
            fclose($socket);
            return false;
        }
    } catch (Exception $e) {
        echo "❌ Exception: " . $e->getMessage() . "\n";
        return false;
    }
}

// Get SMTP settings from command line or use defaults
$host = $argv[1] ?? 'mail.nurulhayah.com';
$port = $argv[2] ?? 465;
$username = $argv[3] ?? '<EMAIL>';
$password = $argv[4] ?? 'NurulHayah4';
$encryption = $argv[5] ?? 'ssl';

// Test the connection
testSmtpConnection($host, $port, $username, $password, $encryption);

// Suggest alternative settings to try
echo "\nIf the connection failed, try these alternative settings:\n";
echo "1. Different ports: 587, 25, 2525\n";
echo "2. Different encryption: ssl, tls, none\n";
echo "3. Check with your hosting provider for the correct SMTP settings\n";
echo "4. Make sure the password is correct and the account has SMTP access enabled\n";
echo "\nUsage: php verify-smtp.php [host] [port] [username] [password] [encryption]\n";
