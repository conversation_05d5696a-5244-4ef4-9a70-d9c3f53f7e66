Uid Component
=============

The UID component provides an object-oriented API to generate and represent UIDs.

It provides implementations that work on 32-bit and 64-bit CPUs
for ULIDs and for UUIDs version 1 and versions 3 to 8.

Resources
---------

 * [Documentation](https://symfony.com/doc/current/components/uid.html)
 * [Contributing](https://symfony.com/doc/current/contributing/index.html)
 * [Report issues](https://github.com/symfony/symfony/issues) and
   [send Pull Requests](https://github.com/symfony/symfony/pulls)
   in the [main Symfony repository](https://github.com/symfony/symfony)
