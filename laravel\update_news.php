<?php

// Bootstrap the <PERSON><PERSON> application
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Update all news to be published
$news = \App\Models\News::all();
$count = 0;

foreach ($news as $article) {
    $article->is_published = true;
    $article->save();
    $count++;
}

echo "Updated $count news articles to be published.\n";
